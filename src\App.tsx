
import { useState, useRef, useEffect } from 'react'

function App() {
  const [glassStyle, setGlassStyle] = useState({
    bg: 'rgba(255, 255, 255, 0.15)',
    border: 'rgba(255, 255, 255, 0.3)',
    shadow: 'rgba(0, 0, 0, 0.2)',
  })

  const canvasRef = useRef<HTMLCanvasElement>(null)

  useEffect(() => {
    const analyzeBackground = () => {
      if (!canvasRef.current) return

      const canvas = canvasRef.current
      const ctx = canvas.getContext('2d')
      const img = new Image()

      img.crossOrigin = 'anonymous'
      img.onload = () => {
        canvas.width = 100
        canvas.height = 100
        ctx?.drawImage(img, 0, 0, 100, 100)

        const imageData = ctx?.getImageData(0, 0, 100, 100)
        if (!imageData) return

        let r = 0, g = 0, b = 0, count = 0

        for (let i = 0; i < imageData.data.length; i += 16) {
          r += imageData.data[i]
          g += imageData.data[i + 1]
          b += imageData.data[i + 2]
          count++
        }

        r = Math.floor(r / count)
        g = Math.floor(g / count)
        b = Math.floor(b / count)

        const brightness = (r * 299 + g * 587 + b * 114) / 1000
        const isDark = brightness < 128

        setGlassStyle({
          bg: isDark
            ? `rgba(${Math.min(r + 60, 255)}, ${Math.min(g + 60, 255)}, ${Math.min(b + 60, 255)}, 0.2)`
            : `rgba(${Math.max(r - 20, 0)}, ${Math.max(g - 20, 0)}, ${Math.max(b - 20, 0)}, 0.3)`,
          border: isDark
            ? `rgba(255, 255, 255, 0.4)`
            : `rgba(${r}, ${g}, ${b}, 0.5)`,
          shadow: isDark
            ? `rgba(255, 255, 255, 0.15)`
            : `rgba(0, 0, 0, 0.25)`,
        })
      }

      img.src = 'https://images.unsplash.com/photo-1503435980610-a51f3ddfee50?q=80&w=687&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D'
    }

    analyzeBackground()
  }, [])

  return (
    <div className="min-h-screen relative flex items-center justify-center p-8 overflow-hidden">
      <canvas ref={canvasRef} className="hidden" />

      <div
        className="absolute inset-0 bg-cover bg-center bg-no-repeat"
        style={{
          backgroundImage: 'url(https://images.unsplash.com/photo-1503435980610-a51f3ddfee50?q=80&w=687&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D)'
        }}
      />

      <div className="relative z-10 max-w-6xl w-full">
        <h1 className="text-4xl md:text-6xl font-bold text-center mb-16 bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 bg-clip-text text-transparent drop-shadow-2xl animate-fade-in">
          Adaptive Liquid Glass
        </h1>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 animate-slide-up">
          <LiquidGlass glassStyle={glassStyle} type="card" />
          <LiquidGlass glassStyle={glassStyle} type="interactive" />
          <LiquidGlass glassStyle={glassStyle} type="floating" />
          <LiquidGlass glassStyle={glassStyle} type="morphing" />
          <LiquidGlass glassStyle={glassStyle} type="ripple" />
          <LiquidGlass glassStyle={glassStyle} type="liquid" />
        </div>
      </div>
    </div>
  )
}

// Pure CSS Liquid Glass Component
function LiquidGlass({ glassStyle, type }: { glassStyle: any, type: string }) {
  const [isHovered, setIsHovered] = useState(false)
  const [mousePos, setMousePos] = useState({ x: 50, y: 50 })
  const [clicks, setClicks] = useState(0)
  const [transform, setTransform] = useState({ x: 0, y: 0, rotateX: 0, rotateY: 0 })
  const containerRef = useRef<HTMLDivElement>(null)

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!containerRef.current) return
    const rect = containerRef.current.getBoundingClientRect()
    const centerX = rect.left + rect.width / 2
    const centerY = rect.top + rect.height / 2

    const x = ((e.clientX - rect.left) / rect.width) * 100
    const y = ((e.clientY - rect.top) / rect.height) * 100

    setMousePos({ x, y })
    setTransform({
      x: (e.clientX - centerX) / 20,
      y: (e.clientY - centerY) / 20,
      rotateX: (e.clientY - centerY) / 30,
      rotateY: (e.clientX - centerX) / 30,
    })
  }

  const handleMouseLeave = () => {
    setTransform({ x: 0, y: 0, rotateX: 0, rotateY: 0 })
    setMousePos({ x: 50, y: 50 })
  }

  const handleClick = () => setClicks(prev => prev + 1)

  const getContent = () => {
    switch (type) {
      case 'card': return { title: 'Adaptive Glass', desc: 'Responds to background colors' }
      case 'interactive': return { title: 'Interactive', desc: `Clicks: ${clicks}` }
      case 'floating': return { title: 'Floating', desc: 'Gentle animations' }
      case 'morphing': return { title: 'Morphing', desc: 'Shape-shifting effects' }
      case 'ripple': return { title: 'Ripple', desc: 'Wave propagation' }
      case 'liquid': return { title: 'Liquid', desc: 'Fluid dynamics' }
      default: return { title: 'Glass', desc: 'Beautiful effect' }
    }
  }

  const content = getContent()

  return (
    <div
      ref={containerRef}
      className="relative group cursor-pointer transition-all duration-300 hover:scale-105"
      style={{
        transform: `translate3d(${transform.x}px, ${transform.y}px, 0) rotateX(${transform.rotateX}deg) rotateY(${transform.rotateY}deg)`,
        transition: isHovered ? 'none' : 'transform 0.5s ease-out',
      }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => { setIsHovered(false); handleMouseLeave() }}
      onMouseMove={handleMouseMove}
      onClick={handleClick}
    >
      {/* Adaptive Glass Container */}
      <div
        className="relative overflow-hidden rounded-3xl p-8 h-64 transform-gpu transition-all duration-500"
        style={{
          background: glassStyle.bg,
          backdropFilter: 'blur(20px)',
          border: `1px solid ${glassStyle.border}`,
          boxShadow: `0 8px 32px ${glassStyle.shadow}, inset 0 1px 0 rgba(255,255,255,0.1)`,
        }}
      >
        {/* Dynamic Radial Gradient */}
        <div
          className="absolute inset-0 transition-opacity duration-300"
          style={{
            background: isHovered
              ? `radial-gradient(circle at ${mousePos.x}% ${mousePos.y}%, rgba(255,255,255,0.2) 0%, transparent 50%)`
              : 'transparent',
            opacity: isHovered ? 1 : 0,
          }}
        />

        {/* Floating Particles */}
        {[...Array(3)].map((_, i) => (
          <div
            key={i}
            className="absolute w-1 h-1 rounded-full animate-float opacity-60"
            style={{
              background: glassStyle.border,
              left: `${20 + i * 25}%`,
              top: `${40 + i * 10}%`,
              animationDelay: `${i * 0.5}s`,
              animationDuration: `${2 + i}s`,
            }}
          />
        ))}

        {/* Liquid Morphing Blob */}
        <div
          className="absolute -top-10 -right-10 w-20 h-20 rounded-full blur-xl opacity-30 animate-morph"
          style={{
            background: glassStyle.border,
            animationDuration: isHovered ? '2s' : '4s',
          }}
        />

        {/* Content */}
        <div className="relative z-10 h-full flex flex-col justify-center">
          <h3
            className="text-xl font-bold text-white mb-2 transition-transform duration-300"
            style={{ transform: isHovered ? 'translateY(-3px)' : 'translateY(0)' }}
          >
            {content.title}
          </h3>
          <p
            className="text-white/80 text-sm transition-transform duration-300"
            style={{
              transform: isHovered ? 'translateY(-3px)' : 'translateY(0)',
              transitionDelay: '0.1s',
            }}
          >
            {content.desc}
          </p>
        </div>

        {/* Shimmer Effect */}
        <div
          className={`absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -skew-x-12 transition-transform duration-1000 ${
            isHovered ? 'translate-x-full' : '-translate-x-full'
          }`}
        />
      </div>
    </div>
  )
}

export default App