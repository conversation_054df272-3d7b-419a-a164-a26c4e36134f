
import { motion, useMotionValue, useTransform } from 'motion/react'
import { useState, useRef, useEffect } from 'react'

// Hook to detect background colors and create adaptive glass styles
function useAdaptiveGlass() {
  const [glassStyle, setGlassStyle] = useState({
    bg: 'rgba(255, 255, 255, 0.1)',
    border: 'rgba(255, 255, 255, 0.2)',
    shadow: 'rgba(0, 0, 0, 0.1)',
    blur: 'blur(20px)',
  })

  const canvasRef = useRef<HTMLCanvasElement>(null)
  const bgRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const analyzeBackground = () => {
      if (!canvasRef.current || !bgRef.current) return

      const canvas = canvasRef.current
      const ctx = canvas.getContext('2d')
      const img = new Image()

      img.crossOrigin = 'anonymous'
      img.onload = () => {
        canvas.width = 100
        canvas.height = 100
        ctx?.drawImage(img, 0, 0, 100, 100)

        const imageData = ctx?.getImageData(0, 0, 100, 100)
        if (!imageData) return

        let r = 0, g = 0, b = 0, count = 0

        // Sample pixels to get average color
        for (let i = 0; i < imageData.data.length; i += 16) {
          r += imageData.data[i]
          g += imageData.data[i + 1]
          b += imageData.data[i + 2]
          count++
        }

        r = Math.floor(r / count)
        g = Math.floor(g / count)
        b = Math.floor(b / count)

        // Calculate brightness
        const brightness = (r * 299 + g * 587 + b * 114) / 1000
        const isDark = brightness < 128

        // Adaptive glass styling based on background
        setGlassStyle({
          bg: isDark
            ? `rgba(${Math.min(r + 50, 255)}, ${Math.min(g + 50, 255)}, ${Math.min(b + 50, 255)}, 0.15)`
            : `rgba(${Math.max(r - 30, 0)}, ${Math.max(g - 30, 0)}, ${Math.max(b - 30, 0)}, 0.25)`,
          border: isDark
            ? `rgba(255, 255, 255, 0.3)`
            : `rgba(${r}, ${g}, ${b}, 0.4)`,
          shadow: isDark
            ? `rgba(255, 255, 255, 0.1)`
            : `rgba(0, 0, 0, 0.2)`,
          blur: isDark ? 'blur(25px)' : 'blur(15px)',
        })
      }

      img.src = 'https://images.unsplash.com/photo-1503435980610-a51f3ddfee50?q=80&w=687&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D'
    }

    analyzeBackground()
  }, [])

  return { glassStyle, canvasRef, bgRef }
}

function App() {
  const { glassStyle, canvasRef, bgRef } = useAdaptiveGlass()

  return (
    <div className="min-h-screen relative flex items-center justify-center p-8 overflow-hidden">
      {/* Hidden canvas for color analysis */}
      <canvas ref={canvasRef} className="hidden" />

      {/* Background Image */}
      <div
        ref={bgRef}
        className="absolute inset-0 bg-cover bg-center bg-no-repeat"
        style={{
          backgroundImage: 'url(https://images.unsplash.com/photo-1503435980610-a51f3ddfee50?q=80&w=687&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D)'
        }}
      />

      <div className="relative z-10 max-w-6xl w-full">
        <motion.h1
          className="text-4xl md:text-6xl font-bold text-center mb-16 bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 bg-clip-text text-transparent drop-shadow-2xl"
          initial={{ opacity: 0, y: -50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1, ease: "easeOut" }}
        >
          Adaptive Liquid Glass
        </motion.h1>

        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1, delay: 0.3, ease: "easeOut" }}
        >
          <AdaptiveGlass glassStyle={glassStyle} type="card" />
          <AdaptiveGlass glassStyle={glassStyle} type="interactive" />
          <AdaptiveGlass glassStyle={glassStyle} type="floating" />
          <AdaptiveGlass glassStyle={glassStyle} type="morphing" />
          <AdaptiveGlass glassStyle={glassStyle} type="ripple" />
          <AdaptiveGlass glassStyle={glassStyle} type="liquid" />
        </motion.div>
      </div>
    </div>
  )
}

// Unified Adaptive Glass Component
function AdaptiveGlass({ glassStyle, type }: { glassStyle: any, type: string }) {
  const [isHovered, setIsHovered] = useState(false)
  const [mousePos, setMousePos] = useState({ x: 0, y: 0 })
  const [clicks, setClicks] = useState(0)
  const containerRef = useRef<HTMLDivElement>(null)

  const x = useMotionValue(0)
  const y = useMotionValue(0)
  const rotateX = useTransform(y, [-100, 100], [10, -10])
  const rotateY = useTransform(x, [-100, 100], [-10, 10])

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!containerRef.current) return
    const rect = containerRef.current.getBoundingClientRect()
    const centerX = rect.left + rect.width / 2
    const centerY = rect.top + rect.height / 2

    setMousePos({
      x: e.clientX - rect.left,
      y: e.clientY - rect.top,
    })

    x.set((e.clientX - centerX) / 10)
    y.set((e.clientY - centerY) / 10)
  }

  const handleClick = () => setClicks(prev => prev + 1)

  const getContent = () => {
    switch (type) {
      case 'card': return { title: 'Adaptive Glass', desc: 'Responds to background colors' }
      case 'interactive': return { title: 'Interactive', desc: `Clicks: ${clicks}` }
      case 'floating': return { title: 'Floating', desc: 'Gentle animations' }
      case 'morphing': return { title: 'Morphing', desc: 'Shape-shifting effects' }
      case 'ripple': return { title: 'Ripple', desc: 'Wave propagation' }
      case 'liquid': return { title: 'Liquid', desc: 'Fluid dynamics' }
      default: return { title: 'Glass', desc: 'Beautiful effect' }
    }
  }

  const content = getContent()

  return (
    <motion.div
      ref={containerRef}
      className="relative group cursor-pointer"
      style={{ x, y, rotateX, rotateY }}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
      onMouseMove={handleMouseMove}
      onMouseLeave={() => { x.set(0); y.set(0) }}
      onClick={handleClick}
      whileHover={{ scale: 1.02, z: 50 }}
      whileTap={{ scale: 0.98 }}
      transition={{ duration: 0.3, ease: "easeOut" }}
    >
      {/* Adaptive Glass Container */}
      <div
        className="relative overflow-hidden rounded-3xl p-8 h-64 transform-gpu"
        style={{
          background: glassStyle.bg,
          backdropFilter: glassStyle.blur,
          border: `1px solid ${glassStyle.border}`,
          boxShadow: `0 8px 32px ${glassStyle.shadow}`,
        }}
      >
        {/* Dynamic Background Effects */}
        <motion.div
          className="absolute inset-0"
          animate={{
            background: isHovered
              ? `radial-gradient(circle at ${mousePos.x}px ${mousePos.y}px, rgba(255,255,255,0.2), transparent 50%)`
              : 'transparent'
          }}
          transition={{ duration: 0.3 }}
        />

        {/* Floating Particles */}
        {[...Array(3)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 rounded-full"
            style={{ background: glassStyle.border }}
            animate={{
              x: [0, 30, 0],
              y: [0, -20, 0],
              opacity: [0.3, 0.8, 0.3],
            }}
            transition={{
              duration: 2 + i,
              repeat: Infinity,
              delay: i * 0.5,
            }}
            style={{
              left: `${20 + i * 25}%`,
              top: `${40 + i * 10}%`,
            }}
          />
        ))}

        {/* Liquid Morphing Effect */}
        <motion.div
          className="absolute -top-10 -right-10 w-20 h-20 rounded-full blur-xl opacity-30"
          style={{ background: glassStyle.border }}
          animate={{
            scale: isHovered ? [1, 1.5, 1] : [1, 1.2, 1],
            x: isHovered ? [-5, 5, -5] : [0, 10, 0],
            y: isHovered ? [5, -5, 5] : [0, -5, 0],
          }}
          transition={{ duration: 3, repeat: Infinity, ease: "easeInOut" }}
        />

        {/* Content */}
        <div className="relative z-10 h-full flex flex-col justify-center">
          <motion.h3
            className="text-xl font-bold text-white mb-2"
            animate={{ y: isHovered ? -3 : 0 }}
            transition={{ duration: 0.3 }}
          >
            {content.title}
          </motion.h3>
          <motion.p
            className="text-white/80 text-sm"
            animate={{ y: isHovered ? -3 : 0 }}
            transition={{ duration: 0.3, delay: 0.1 }}
          >
            {content.desc}
          </motion.p>
        </div>

        {/* Shimmer Effect */}
        <motion.div
          className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -skew-x-12"
          initial={{ x: "-100%" }}
          animate={{ x: isHovered ? "200%" : "-100%" }}
          transition={{ duration: 1.5, ease: "easeInOut" }}
        />
      </div>
    </motion.div>
  )
}

// Liquid Glass Button Component
function LiquidGlassButton() {
  const [isPressed, setIsPressed] = useState(false)

  return (
    <div className="flex items-center justify-center h-64">
      <motion.button
        className="relative overflow-hidden rounded-2xl bg-white/10 backdrop-blur-xl border border-white/20 px-8 py-4 text-white font-semibold"
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        onTapStart={() => setIsPressed(true)}
        onTapCancel={() => setIsPressed(false)}
        onTap={() => setIsPressed(false)}
      >
        {/* Liquid Background */}
        <motion.div
          className="absolute inset-0 bg-gradient-to-r from-blue-400/20 via-purple-400/20 to-pink-400/20"
          animate={{
            scale: isPressed ? 1.1 : 1,
            opacity: isPressed ? 0.8 : 0.5,
          }}
          transition={{ duration: 0.2 }}
        />

        {/* Ripple Effect */}
        <motion.div
          className="absolute inset-0 bg-white/20 rounded-full"
          initial={{ scale: 0, opacity: 0 }}
          animate={{
            scale: isPressed ? 2 : 0,
            opacity: isPressed ? [0, 0.5, 0] : 0
          }}
          transition={{ duration: 0.6 }}
        />

        <span className="relative z-10">Liquid Button</span>
      </motion.button>
    </div>
  )
}

// Liquid Glass Panel Component
function LiquidGlassPanel() {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 })
  const panelRef = useRef<HTMLDivElement>(null)

  const handleMouseMove = (e: React.MouseEvent) => {
    if (panelRef.current) {
      const rect = panelRef.current.getBoundingClientRect()
      setMousePosition({
        x: e.clientX - rect.left,
        y: e.clientY - rect.top,
      })
    }
  }

  return (
    <motion.div
      ref={panelRef}
      className="relative overflow-hidden rounded-3xl bg-white/5 backdrop-blur-xl border border-white/10 h-64 cursor-none"
      onMouseMove={handleMouseMove}
      whileHover={{ borderColor: "rgba(255, 255, 255, 0.3)" }}
    >
      {/* Mouse Follower Liquid Blob */}
      <motion.div
        className="absolute w-32 h-32 bg-gradient-to-br from-cyan-400/30 to-blue-400/30 rounded-full blur-xl pointer-events-none"
        animate={{
          x: mousePosition.x - 64,
          y: mousePosition.y - 64,
        }}
        transition={{ type: "spring", damping: 30, stiffness: 200 }}
      />

      {/* Content */}
      <div className="relative z-10 p-8 h-full flex flex-col justify-center">
        <h3 className="text-xl font-bold text-white mb-2">Interactive Panel</h3>
        <p className="text-white/70">Move your mouse to see the liquid follow</p>
      </div>
    </motion.div>
  )
}

// Liquid Glass Hover Component
function LiquidGlassHover() {
  const x = useMotionValue(0)
  const y = useMotionValue(0)
  const rotateX = useTransform(y, [-100, 100], [30, -30])
  const rotateY = useTransform(x, [-100, 100], [-30, 30])

  return (
    <motion.div
      className="relative perspective-1000"
      style={{ x, y, rotateX, rotateY }}
      whileHover={{ z: 50 }}
      onMouseMove={(e) => {
        const rect = e.currentTarget.getBoundingClientRect()
        const centerX = rect.left + rect.width / 2
        const centerY = rect.top + rect.height / 2
        x.set((e.clientX - centerX) / 5)
        y.set((e.clientY - centerY) / 5)
      }}
      onMouseLeave={() => {
        x.set(0)
        y.set(0)
      }}
    >
      <div className="relative overflow-hidden rounded-3xl bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-xl border border-white/20 p-8 h-64 transform-gpu">
        {/* Floating Particles */}
        {[...Array(6)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-2 h-2 bg-white/30 rounded-full"
            animate={{
              x: [0, 50, 0],
              y: [0, -30, 0],
              opacity: [0.3, 0.8, 0.3],
            }}
            transition={{
              duration: 3 + i * 0.5,
              repeat: Infinity,
              delay: i * 0.3,
            }}
            style={{
              left: `${20 + i * 15}%`,
              top: `${30 + i * 10}%`,
            }}
          />
        ))}

        <div className="relative z-10">
          <h3 className="text-xl font-bold text-white mb-2">3D Tilt Effect</h3>
          <p className="text-white/70">Hover to see the 3D transformation</p>
        </div>
      </div>
    </motion.div>
  )
}

// Interactive Liquid Glass Component
function LiquidGlassInteractive() {
  const [clicks, setClicks] = useState(0)
  const [ripples, setRipples] = useState<Array<{ id: number; x: number; y: number }>>([])

  const handleClick = (e: React.MouseEvent) => {
    const rect = e.currentTarget.getBoundingClientRect()
    const newRipple = {
      id: Date.now(),
      x: e.clientX - rect.left,
      y: e.clientY - rect.top,
    }

    setRipples(prev => [...prev, newRipple])
    setClicks(prev => prev + 1)

    // Remove ripple after animation
    setTimeout(() => {
      setRipples(prev => prev.filter(ripple => ripple.id !== newRipple.id))
    }, 1000)
  }

  return (
    <motion.div
      className="relative overflow-hidden rounded-3xl bg-white/8 backdrop-blur-xl border border-white/15 h-64 cursor-pointer"
      onClick={handleClick}
      whileTap={{ scale: 0.98 }}
    >
      {/* Ripple Effects */}
      {ripples.map((ripple) => (
        <motion.div
          key={ripple.id}
          className="absolute bg-white/20 rounded-full pointer-events-none"
          initial={{ scale: 0, opacity: 0.8 }}
          animate={{ scale: 4, opacity: 0 }}
          transition={{ duration: 1, ease: "easeOut" }}
          style={{
            left: ripple.x - 25,
            top: ripple.y - 25,
            width: 50,
            height: 50,
          }}
        />
      ))}

      {/* Dynamic Background */}
      <motion.div
        className="absolute inset-0 bg-gradient-to-br from-emerald-400/20 via-teal-400/20 to-cyan-400/20"
        animate={{
          opacity: [0.2, 0.4, 0.2],
          scale: [1, 1.05, 1],
        }}
        transition={{
          duration: 2,
          repeat: Infinity,
          ease: "easeInOut",
        }}
      />

      <div className="relative z-10 p-8 h-full flex flex-col justify-center">
        <h3 className="text-xl font-bold text-white mb-2">Click Me!</h3>
        <p className="text-white/70">Clicks: {clicks}</p>
        <p className="text-white/50 text-sm mt-2">Each click creates a ripple effect</p>
      </div>
    </motion.div>
  )
}

// Floating Liquid Glass Component
function LiquidGlassFloating() {
  return (
    <div className="relative h-64 flex items-center justify-center">
      <motion.div
        className="relative overflow-hidden rounded-3xl bg-white/10 backdrop-blur-xl border border-white/20 p-8 w-full h-48"
        animate={{
          y: [-5, 5, -5],
          rotateX: [0, 2, 0],
          rotateY: [0, -2, 0],
        }}
        transition={{
          duration: 4,
          repeat: Infinity,
          ease: "easeInOut",
        }}
      >
        {/* Floating Liquid Orbs */}
        {[...Array(4)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute bg-gradient-to-br from-purple-400/40 to-pink-400/40 rounded-full blur-sm"
            animate={{
              x: [0, 30, 0],
              y: [0, -20, 0],
              scale: [1, 1.2, 1],
            }}
            transition={{
              duration: 3 + i * 0.5,
              repeat: Infinity,
              delay: i * 0.7,
              ease: "easeInOut",
            }}
            style={{
              width: `${20 + i * 5}px`,
              height: `${20 + i * 5}px`,
              left: `${20 + i * 20}%`,
              top: `${30 + i * 15}%`,
            }}
          />
        ))}

        <div className="relative z-10">
          <h3 className="text-xl font-bold text-white mb-2">Floating Glass</h3>
          <p className="text-white/70">Gentle floating animation with liquid orbs</p>
        </div>
      </motion.div>
    </div>
  )
}

export default App