
import { motion, useMotionValue, useTransform } from 'motion/react'
import { useState, useRef } from 'react'

function App() {
  return (
    <div className="min-h-screen relative flex items-center justify-center p-8 overflow-hidden">
      {/* Background Image with Overlay */}
      <div
        className="absolute inset-0 bg-cover bg-center bg-no-repeat"
        style={{
          backgroundImage: 'url(https://images.unsplash.com/photo-1503435980610-a51f3ddfee50?q=80&w=687&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D)'
        }}
      />

      {/* Dark Overlay for Better Contrast */}
      <div className="absolute inset-0 bg-black/40 backdrop-blur-[1px]" />

      {/* Gradient Overlay for Enhanced Glass Effect */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-900/20 via-purple-900/30 to-pink-900/20" />

      <div className="relative z-10 max-w-6xl w-full">
        <motion.h1
          className="text-4xl md:text-6xl font-bold text-center mb-16 bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 bg-clip-text text-transparent drop-shadow-2xl"
          initial={{ opacity: 0, y: -50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1, ease: "easeOut" }}
        >
          Apple Liquid Glass Effect
        </motion.h1>

        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1, delay: 0.3, ease: "easeOut" }}
        >
          <LiquidGlassCard />
          <LiquidGlassButton />
          <LiquidGlassPanel />
          <LiquidGlassHover />
          <LiquidGlassInteractive />
          <LiquidGlassFloating />
        </motion.div>
      </div>
    </div>
  )
}

// Main Liquid Glass Card Component
function LiquidGlassCard() {
  const [isHovered, setIsHovered] = useState(false)

  return (
    <motion.div
      className="relative group"
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
      whileHover={{ scale: 1.02 }}
      transition={{ duration: 0.3, ease: "easeOut" }}
    >
      {/* Glass Container */}
      <div className="relative overflow-hidden rounded-3xl bg-white/15 backdrop-blur-2xl border border-white/30 p-8 h-64 shadow-2xl">
        {/* Animated Background Gradient */}
        <motion.div
          className="absolute inset-0 bg-gradient-to-br from-blue-400/20 via-purple-400/20 to-pink-400/20"
          animate={{
            background: isHovered
              ? "linear-gradient(135deg, rgba(59, 130, 246, 0.3), rgba(147, 51, 234, 0.3), rgba(236, 72, 153, 0.3))"
              : "linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(147, 51, 234, 0.1), rgba(236, 72, 153, 0.1))"
          }}
          transition={{ duration: 0.5 }}
        />

        {/* Liquid Blob Animation */}
        <motion.div
          className="absolute -top-20 -right-20 w-40 h-40 bg-gradient-to-br from-blue-400/30 to-purple-400/30 rounded-full blur-xl"
          animate={{
            x: isHovered ? -10 : 0,
            y: isHovered ? 10 : 0,
            scale: isHovered ? 1.2 : 1,
          }}
          transition={{ duration: 0.8, ease: "easeInOut" }}
        />

        {/* Content */}
        <div className="relative z-10">
          <motion.h3
            className="text-2xl font-bold text-white mb-4"
            animate={{ y: isHovered ? -5 : 0 }}
            transition={{ duration: 0.3 }}
          >
            Liquid Glass
          </motion.h3>
          <motion.p
            className="text-white/80 leading-relaxed"
            animate={{ y: isHovered ? -5 : 0 }}
            transition={{ duration: 0.3, delay: 0.1 }}
          >
            Experience the fluid beauty of Apple's design language with this mesmerizing glass effect.
          </motion.p>
        </div>

        {/* Shimmer Effect */}
        <motion.div
          className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -skew-x-12"
          initial={{ x: "-100%" }}
          animate={{ x: isHovered ? "200%" : "-100%" }}
          transition={{ duration: 1.5, ease: "easeInOut" }}
        />
      </div>
    </motion.div>
  )
}

// Liquid Glass Button Component
function LiquidGlassButton() {
  const [isPressed, setIsPressed] = useState(false)

  return (
    <div className="flex items-center justify-center h-64">
      <motion.button
        className="relative overflow-hidden rounded-2xl bg-white/10 backdrop-blur-xl border border-white/20 px-8 py-4 text-white font-semibold"
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        onTapStart={() => setIsPressed(true)}
        onTapCancel={() => setIsPressed(false)}
        onTap={() => setIsPressed(false)}
      >
        {/* Liquid Background */}
        <motion.div
          className="absolute inset-0 bg-gradient-to-r from-blue-400/20 via-purple-400/20 to-pink-400/20"
          animate={{
            scale: isPressed ? 1.1 : 1,
            opacity: isPressed ? 0.8 : 0.5,
          }}
          transition={{ duration: 0.2 }}
        />

        {/* Ripple Effect */}
        <motion.div
          className="absolute inset-0 bg-white/20 rounded-full"
          initial={{ scale: 0, opacity: 0 }}
          animate={{
            scale: isPressed ? 2 : 0,
            opacity: isPressed ? [0, 0.5, 0] : 0
          }}
          transition={{ duration: 0.6 }}
        />

        <span className="relative z-10">Liquid Button</span>
      </motion.button>
    </div>
  )
}

// Liquid Glass Panel Component
function LiquidGlassPanel() {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 })
  const panelRef = useRef<HTMLDivElement>(null)

  const handleMouseMove = (e: React.MouseEvent) => {
    if (panelRef.current) {
      const rect = panelRef.current.getBoundingClientRect()
      setMousePosition({
        x: e.clientX - rect.left,
        y: e.clientY - rect.top,
      })
    }
  }

  return (
    <motion.div
      ref={panelRef}
      className="relative overflow-hidden rounded-3xl bg-white/5 backdrop-blur-xl border border-white/10 h-64 cursor-none"
      onMouseMove={handleMouseMove}
      whileHover={{ borderColor: "rgba(255, 255, 255, 0.3)" }}
    >
      {/* Mouse Follower Liquid Blob */}
      <motion.div
        className="absolute w-32 h-32 bg-gradient-to-br from-cyan-400/30 to-blue-400/30 rounded-full blur-xl pointer-events-none"
        animate={{
          x: mousePosition.x - 64,
          y: mousePosition.y - 64,
        }}
        transition={{ type: "spring", damping: 30, stiffness: 200 }}
      />

      {/* Content */}
      <div className="relative z-10 p-8 h-full flex flex-col justify-center">
        <h3 className="text-xl font-bold text-white mb-2">Interactive Panel</h3>
        <p className="text-white/70">Move your mouse to see the liquid follow</p>
      </div>
    </motion.div>
  )
}

// Liquid Glass Hover Component
function LiquidGlassHover() {
  const x = useMotionValue(0)
  const y = useMotionValue(0)
  const rotateX = useTransform(y, [-100, 100], [30, -30])
  const rotateY = useTransform(x, [-100, 100], [-30, 30])

  return (
    <motion.div
      className="relative perspective-1000"
      style={{ x, y, rotateX, rotateY }}
      whileHover={{ z: 50 }}
      onMouseMove={(e) => {
        const rect = e.currentTarget.getBoundingClientRect()
        const centerX = rect.left + rect.width / 2
        const centerY = rect.top + rect.height / 2
        x.set((e.clientX - centerX) / 5)
        y.set((e.clientY - centerY) / 5)
      }}
      onMouseLeave={() => {
        x.set(0)
        y.set(0)
      }}
    >
      <div className="relative overflow-hidden rounded-3xl bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-xl border border-white/20 p-8 h-64 transform-gpu">
        {/* Floating Particles */}
        {[...Array(6)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-2 h-2 bg-white/30 rounded-full"
            animate={{
              x: [0, 50, 0],
              y: [0, -30, 0],
              opacity: [0.3, 0.8, 0.3],
            }}
            transition={{
              duration: 3 + i * 0.5,
              repeat: Infinity,
              delay: i * 0.3,
            }}
            style={{
              left: `${20 + i * 15}%`,
              top: `${30 + i * 10}%`,
            }}
          />
        ))}

        <div className="relative z-10">
          <h3 className="text-xl font-bold text-white mb-2">3D Tilt Effect</h3>
          <p className="text-white/70">Hover to see the 3D transformation</p>
        </div>
      </div>
    </motion.div>
  )
}

// Interactive Liquid Glass Component
function LiquidGlassInteractive() {
  const [clicks, setClicks] = useState(0)
  const [ripples, setRipples] = useState<Array<{ id: number; x: number; y: number }>>([])

  const handleClick = (e: React.MouseEvent) => {
    const rect = e.currentTarget.getBoundingClientRect()
    const newRipple = {
      id: Date.now(),
      x: e.clientX - rect.left,
      y: e.clientY - rect.top,
    }

    setRipples(prev => [...prev, newRipple])
    setClicks(prev => prev + 1)

    // Remove ripple after animation
    setTimeout(() => {
      setRipples(prev => prev.filter(ripple => ripple.id !== newRipple.id))
    }, 1000)
  }

  return (
    <motion.div
      className="relative overflow-hidden rounded-3xl bg-white/8 backdrop-blur-xl border border-white/15 h-64 cursor-pointer"
      onClick={handleClick}
      whileTap={{ scale: 0.98 }}
    >
      {/* Ripple Effects */}
      {ripples.map((ripple) => (
        <motion.div
          key={ripple.id}
          className="absolute bg-white/20 rounded-full pointer-events-none"
          initial={{ scale: 0, opacity: 0.8 }}
          animate={{ scale: 4, opacity: 0 }}
          transition={{ duration: 1, ease: "easeOut" }}
          style={{
            left: ripple.x - 25,
            top: ripple.y - 25,
            width: 50,
            height: 50,
          }}
        />
      ))}

      {/* Dynamic Background */}
      <motion.div
        className="absolute inset-0 bg-gradient-to-br from-emerald-400/20 via-teal-400/20 to-cyan-400/20"
        animate={{
          opacity: [0.2, 0.4, 0.2],
          scale: [1, 1.05, 1],
        }}
        transition={{
          duration: 2,
          repeat: Infinity,
          ease: "easeInOut",
        }}
      />

      <div className="relative z-10 p-8 h-full flex flex-col justify-center">
        <h3 className="text-xl font-bold text-white mb-2">Click Me!</h3>
        <p className="text-white/70">Clicks: {clicks}</p>
        <p className="text-white/50 text-sm mt-2">Each click creates a ripple effect</p>
      </div>
    </motion.div>
  )
}

// Floating Liquid Glass Component
function LiquidGlassFloating() {
  return (
    <div className="relative h-64 flex items-center justify-center">
      <motion.div
        className="relative overflow-hidden rounded-3xl bg-white/10 backdrop-blur-xl border border-white/20 p-8 w-full h-48"
        animate={{
          y: [-5, 5, -5],
          rotateX: [0, 2, 0],
          rotateY: [0, -2, 0],
        }}
        transition={{
          duration: 4,
          repeat: Infinity,
          ease: "easeInOut",
        }}
      >
        {/* Floating Liquid Orbs */}
        {[...Array(4)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute bg-gradient-to-br from-purple-400/40 to-pink-400/40 rounded-full blur-sm"
            animate={{
              x: [0, 30, 0],
              y: [0, -20, 0],
              scale: [1, 1.2, 1],
            }}
            transition={{
              duration: 3 + i * 0.5,
              repeat: Infinity,
              delay: i * 0.7,
              ease: "easeInOut",
            }}
            style={{
              width: `${20 + i * 5}px`,
              height: `${20 + i * 5}px`,
              left: `${20 + i * 20}%`,
              top: `${30 + i * 15}%`,
            }}
          />
        ))}

        <div className="relative z-10">
          <h3 className="text-xl font-bold text-white mb-2">Floating Glass</h3>
          <p className="text-white/70">Gentle floating animation with liquid orbs</p>
        </div>
      </motion.div>
    </div>
  )
}

export default App