
function App() {
  return (
    <div className="min-h-screen relative flex items-center justify-center p-8">
      {/* Background Image */}
      <div
        className="absolute inset-0 bg-cover bg-center bg-no-repeat"
        style={{
          backgroundImage: 'url(https://images.unsplash.com/photo-1503435980610-a51f3ddfee50?q=80&w=687&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D)'
        }}
      />

      {/* Glass Panel */}
      <div className="relative z-10">
        <GlassPanel />
      </div>
    </div>
  )
}

// Simple Glass Panel Component (like in the image)
function GlassPanel() {
  return (
    <div className="w-96 p-6 rounded-2xl backdrop-blur-md bg-white/10 border border-white/20 shadow-xl">
      {/* Mode Section */}
      <div className="mb-6">
        <label className="block text-white text-sm font-medium mb-2">Mode</label>
        <div className="bg-green-600/80 text-white px-4 py-2 rounded-lg text-sm font-medium">
          Standard
        </div>
      </div>

      {/* Scale Section */}
      <div className="mb-6">
        <div className="flex justify-between items-center mb-2">
          <label className="text-white text-sm font-medium">Scale</label>
          <span className="text-white text-sm">38</span>
        </div>
        <div className="relative">
          <input
            type="range"
            min="0"
            max="200"
            defaultValue="38"
            className="w-full h-2 bg-white/20 rounded-lg appearance-none cursor-pointer slider"
          />
          <div className="flex justify-between text-white/60 text-xs mt-1">
            <span>0</span>
            <span>200</span>
          </div>
        </div>
      </div>

      {/* Blur Section */}
      <div className="mb-6">
        <div className="flex justify-between items-center mb-2">
          <label className="text-white text-sm font-medium">Blur</label>
          <span className="text-white text-sm">50</span>
        </div>
        <div className="relative">
          <input
            type="range"
            min="0"
            max="50"
            defaultValue="50"
            className="w-full h-2 bg-white/20 rounded-lg appearance-none cursor-pointer slider"
          />
          <div className="flex justify-between text-white/60 text-xs mt-1">
            <span>0</span>
            <span>50</span>
          </div>
        </div>
      </div>

      {/* Saturation Section */}
      <div className="mb-6">
        <div className="flex justify-between items-center mb-2">
          <label className="text-white text-sm font-medium">Saturation</label>
          <span className="text-white text-sm">141</span>
        </div>
        <div className="relative">
          <input
            type="range"
            min="0"
            max="300"
            defaultValue="141"
            className="w-full h-2 bg-white/20 rounded-lg appearance-none cursor-pointer slider"
          />
          <div className="flex justify-between text-white/60 text-xs mt-1">
            <span>0</span>
            <span>300</span>
          </div>
        </div>
      </div>

      {/* Aberration Section */}
      <div>
        <div className="flex justify-between items-center mb-2">
          <label className="text-white text-sm font-medium">Aberration</label>
          <span className="text-white text-sm">200</span>
        </div>
        <div className="relative">
          <input
            type="range"
            min="0"
            max="1000"
            defaultValue="200"
            className="w-full h-2 bg-white/20 rounded-lg appearance-none cursor-pointer slider"
          />
          <div className="flex justify-between text-white/60 text-xs mt-1">
            <span>0</span>
            <span>1000</span>
          </div>
        </div>
      </div>
    </div>
  )
}

export default App