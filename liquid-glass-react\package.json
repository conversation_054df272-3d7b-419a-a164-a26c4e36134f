{"name": "liquid-glass-react", "version": "1.1.1", "description": "Apple's Liquid Glass effect for React", "main": "dist/index.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "files": ["dist"], "workspaces": ["liquid-glass"], "scripts": {"build": "npm run clean && npm run build:esm && npm run build:cjs && npm run build:types", "build:esm": "esbuild src/index.tsx --bundle --format=esm --outfile=dist/index.esm.js --external:react --external:react-dom", "build:cjs": "esbuild src/index.tsx --bundle --format=cjs --outfile=dist/index.js --external:react --external:react-dom", "build:types": "tsc --emitDeclarationOnly --outDir dist", "clean": "rm -rf dist", "dev": "npm run build:esm -- --watch", "prepublishOnly": "npm run build"}, "peerDependencies": {"react": ">=19", "react-dom": ">=19"}, "devDependencies": {"@biomejs/biome": "^1.9.4", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "esbuild": "^0.19.0", "react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "^5.0.0"}, "keywords": ["react", "component", "library", "typescript"], "author": "", "license": "MIT", "repository": {"type": "git", "url": ""}}